/**
 * main.js - Application initialization and coordination
 * Initializes all modules and coordinates their interactions
 */

class BillboardEditor {
    constructor() {
        this.modules = {};
        this.isInitialized = false;
        this.config = {
            canvasId: 'billboard-canvas',
            canvasWidth: 800,
            canvasHeight: 400
        };
        
        this.init();
    }

    /**
     * Initialize the billboard editor application
     */
    async init() {
        try {
            console.log('Initializing Billboard Editor...');
            
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.initializeModules());
            } else {
                this.initializeModules();
            }
            
        } catch (error) {
            console.error('Failed to initialize Billboard Editor:', error);
            this.showError('Failed to initialize editor. Please refresh the page.');
        }
    }

    /**
     * Initialize all modules in the correct order
     */
    initializeModules() {
        try {
            console.log('🔄 Starting module initialization...');

            // 1. Initialize responsive manager first
            console.log('🔄 Initializing ResponsiveManager...');
            this.modules.responsive = new ResponsiveManager();
            console.log('✅ ResponsiveManager initialized');

            // 2. Initialize canvas manager
            console.log('🔄 Initializing CanvasManager...');
            this.modules.canvas = new CanvasManager(this.config.canvasId, {
                width: this.config.canvasWidth,
                height: this.config.canvasHeight
            });
            console.log('✅ CanvasManager initialized');

            // 3. Initialize image replacement manager
            console.log('🔄 Initializing ImageReplacementManager...');
            this.modules.imageReplacement = new ImageReplacementManager(this.modules.canvas);
            console.log('✅ ImageReplacementManager initialized');

            // 3. Initialize event handler
            console.log('🔄 Initializing EventHandler...');
            this.modules.events = new EventHandler(this.modules.canvas);
            console.log('✅ EventHandler initialized');

            // 4. Initialize template manager (using existing one)
            console.log('🔄 Checking TemplateManager...');
            if (!window.templateManager) {
                throw new Error('TemplateManager not found. Make sure template-manager.js is loaded.');
            }
            this.modules.templates = window.templateManager;
            console.log('✅ TemplateManager connected');

            // 4. Initialize background manager (after template manager is available)
            console.log('🔄 Initializing BackgroundManager...');
            this.modules.background = new BackgroundManager(this.modules.canvas, this.modules.templates);
            console.log('✅ BackgroundManager initialized');

            // Set up module communication
            console.log('🔄 Setting up module communication...');
            this.setupModuleCommunication();

            // Set up UI event handlers
            console.log('🔄 Setting up UI event handlers...');
            this.setupUIHandlers();

            // Initialize UI state
            console.log('🔄 Initializing UI state...');
            this.initializeUI();

            this.isInitialized = true;
            console.log('🎉 Billboard Editor initialized successfully');

            // Emit ready event
            this.emit('editor:ready');

        } catch (error) {
            console.error('❌ Error initializing modules:', error);
            this.showError(`Error initializing editor: ${error.message}`);
        }
    }

    /**
     * Set up communication between modules
     */
    setupModuleCommunication() {
        // Listen for responsive changes
        document.addEventListener('responsive:breakpoint:change', (e) => {
            this.handleBreakpointChange(e.detail);
        });

        document.addEventListener('responsive:orientation:change', (e) => {
            this.handleOrientationChange(e.detail);
        });

        // Listen for canvas events
        document.addEventListener('editor:object:selected', (e) => {
            this.handleObjectSelection(e.detail);
        });

        document.addEventListener('editor:object:deselected', (e) => {
            this.handleObjectDeselection(e.detail);
        });

        document.addEventListener('editor:object:modified', (e) => {
            this.handleObjectModification(e.detail);
        });

        // Listen for template events
        document.addEventListener('template:selected', (e) => {
            this.handleTemplateSelection(e.detail);
        });
    }

    /**
     * Set up UI event handlers
     */
    setupUIHandlers() {
        console.log('🔄 Setting up UI event handlers...');

        // Category selection
        const categorySelect = document.getElementById('categorySelect');
        if (categorySelect) {
            categorySelect.addEventListener('change', (e) => {
                console.log('📂 Category changed:', e.target.value);
                this.handleCategoryChange(e.target.value);
            });
            console.log('✅ Category select handler bound');
        } else {
            console.warn('⚠️ Category select element not found');
        }

        // Template selection - Use event delegation for dynamically created elements
        document.addEventListener('click', (e) => {
            // Check if clicked element or its parent is a template option
            const templateOption = e.target.closest('.template-option');
            if (templateOption) {
                console.log('🎨 Template clicked via delegation:', templateOption);
                console.log('🎨 Template data:', {
                    id: templateOption.dataset.templateId,
                    category: templateOption.dataset.category
                });
                this.handleTemplateClick(templateOption);
                return;
            }

            // Also check for direct class match (fallback)
            if (e.target.classList.contains('template-option')) {
                console.log('🎨 Template clicked directly:', e.target);
                this.handleTemplateClick(e.target);
                return;
            }
        });
        console.log('✅ Template click handler bound (event delegation)');

        // Export button
        const exportBtn = document.getElementById('exportBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                console.log('📤 Export button clicked');
                this.handleExport();
            });
            console.log('✅ Export button handler bound');
        } else {
            console.warn('⚠️ Export button not found');
        }

        // Clear canvas button
        const clearBtn = document.getElementById('clearBtn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                console.log('🗑️ Clear button clicked');
                this.handleClearCanvas();
            });
            console.log('✅ Clear button handler bound');
        } else {
            console.warn('⚠️ Clear button not found');
        }





        // Image replacement button
        const selectImageBtn = document.getElementById('selectImageBtn');
        const imageReplacementInput = document.getElementById('imageReplacementInput');

        if (selectImageBtn && imageReplacementInput) {
            selectImageBtn.addEventListener('click', () => {
                imageReplacementInput.click();
            });

            imageReplacementInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    this.handleImageReplacement(file);
                }
            });

            console.log('✅ Image replacement handlers bound');
        } else {
            console.warn('⚠️ Image replacement elements not found');
        }

        // Bind image replacement events
        this.bindImageReplacementEvents();
    }

    /**
     * Bind image replacement events
     */
    bindImageReplacementEvents() {
        // Listen for image replacement success
        document.addEventListener('image:replaced', (e) => {
            this.showSuccess(e.detail.message);
        });

        // Listen for image replacement errors
        document.addEventListener('image:error', (e) => {
            this.showError(e.detail.message);
        });

        // Listen for background change success
        document.addEventListener('background:changed', (e) => {
            this.showSuccess(e.detail.message);
        });

        // Listen for background change errors
        document.addEventListener('background:error', (e) => {
            this.showError(e.detail.message);
        });

        console.log('✅ Image replacement and background events bound');
    }

    /**
     * Initialize UI state
     */
    initializeUI() {
        console.log('🔄 Initializing UI state...');

        // Set up responsive UI classes
        const state = this.modules.responsive.getState();
        this.updateUIForBreakpoint(state);

        // Initialize category dropdown
        this.initializeCategoryDropdown();

        // Initialize template grid
        this.updateTemplateGrid();

        // Set initial canvas state
        this.updateCanvasUI();

        console.log('✅ UI state initialized');
    }

    /**
     * Initialize category dropdown
     */
    initializeCategoryDropdown() {
        const categorySelect = document.getElementById('categorySelect');
        if (!categorySelect) {
            console.warn('⚠️ Category select element not found');
            return;
        }

        if (!this.modules.templates) {
            console.error('❌ Template manager not available');
            return;
        }

        try {
            const categories = this.modules.templates.getCategories();
            console.log(`📂 Found ${categories.length} categories:`, categories);

            // Clear existing options (except the first placeholder)
            while (categorySelect.children.length > 1) {
                categorySelect.removeChild(categorySelect.lastChild);
            }

            // Add category options
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category.charAt(0).toUpperCase() + category.slice(1);
                categorySelect.appendChild(option);
            });

            console.log('✅ Category dropdown populated');

        } catch (error) {
            console.error('❌ Error initializing category dropdown:', error);
        }
    }

    /**
     * Handle breakpoint changes
     */
    handleBreakpointChange(detail) {
        console.log('Breakpoint changed:', detail);
        this.updateUIForBreakpoint(detail.state);
        
        // Adjust canvas for new breakpoint
        if (this.modules.canvas) {
            // Canvas will auto-resize via ResponsiveManager
            setTimeout(() => {
                this.modules.canvas.updateCanvasSize();
            }, 100);
        }
    }

    /**
     * Handle orientation changes
     */
    handleOrientationChange(detail) {
        console.log('Orientation changed:', detail);
        
        // Adjust UI layout for orientation
        setTimeout(() => {
            if (this.modules.canvas) {
                this.modules.canvas.updateCanvasSize();
            }
            this.updateTemplateGrid();
        }, 200);
    }

    /**
     * Update UI for current breakpoint
     */
    updateUIForBreakpoint(state) {
        const container = document.querySelector('.editor-container');
        if (!container) return;
        
        // Add responsive classes
        container.classList.toggle('mobile-layout', state.isMobile);
        container.classList.toggle('tablet-layout', state.isTablet);
        container.classList.toggle('desktop-layout', state.isDesktop);
        
        // Adjust control panel layout
        const controlPanel = document.querySelector('.control-panel');
        if (controlPanel) {
            if (state.isMobile) {
                controlPanel.classList.add('mobile-controls');
            } else {
                controlPanel.classList.remove('mobile-controls');
            }
        }
    }

    /**
     * Handle category selection change
     */
    handleCategoryChange(category) {
        console.log('📂 handleCategoryChange called with:', category);

        if (!category) {
            console.log('📂 No category selected, clearing grid');
            this.clearTemplateGrid();
            return;
        }

        try {
            console.log('📂 Category selected:', category);
            this.modules.templates.setCurrentCategory(category);
            this.loadTemplatesForCategory(category);
        } catch (error) {
            console.error('❌ Error handling category change:', error);
            this.showError(`Failed to load category: ${error.message}`);
        }
    }

    /**
     * Load templates for selected category
     */
    loadTemplatesForCategory(category) {
        const templates = this.modules.templates.getTemplatesForCategory(category);
        const templateGrid = document.getElementById('templateGrid');
        
        if (!templateGrid) return;
        
        // Clear existing templates
        templateGrid.innerHTML = '';
        
        // Add templates to grid
        Object.keys(templates).forEach(templateId => {
            const template = templates[templateId];
            const templateElement = this.createTemplateElement(templateId, template);
            templateGrid.appendChild(templateElement);
        });
        
        // Update grid layout for current breakpoint
        this.updateTemplateGrid();
    }

    /**
     * Create template element for grid
     */
    createTemplateElement(templateId, template) {
        console.log(`🎨 Creating template element: ${templateId}`, template);

        const div = document.createElement('div');
        div.className = 'template-option';
        div.dataset.templateId = templateId;
        div.dataset.category = this.modules.templates.currentCategory;

        // Add direct click handler as backup
        div.addEventListener('click', (e) => {
            console.log('🖱️ Template element clicked directly:', templateId);
            e.stopPropagation(); // Prevent event bubbling

            // Call the main handler directly
            if (window.billboardEditor) {
                window.billboardEditor.handleTemplateClick(div);
            }
        });

        // Create preview image or placeholder
        const preview = document.createElement('div');
        preview.className = 'template-preview';

        if (template.background) {
            preview.style.backgroundImage = `url(${template.background})`;
            preview.style.backgroundSize = 'cover';
            preview.style.backgroundPosition = 'center';
            console.log(`🖼️ Template ${templateId} has background: ${template.background}`);
        } else if (template.defaultBackground) {
            preview.style.backgroundColor = template.defaultBackground;
            console.log(`🎨 Template ${templateId} has default background: ${template.defaultBackground}`);
        } else {
            preview.style.backgroundColor = '#f0f0f0';
            console.log(`⚪ Template ${templateId} using fallback background`);
        }

        // Add template info
        const info = document.createElement('div');
        info.className = 'template-info';
        info.innerHTML = `
            <span class="template-type">${template.type || 'unknown'}</span>
            <span class="template-id">${templateId}</span>
        `;

        div.appendChild(preview);
        div.appendChild(info);

        console.log(`✅ Template element created for ${templateId}`);
        return div;
    }

    /**
     * Handle template selection
     */
    handleTemplateClick(element) {
        console.log('🎨 handleTemplateClick called with:', element);

        const templateId = element.dataset.templateId;
        const category = element.dataset.category;

        console.log('📋 Template data:', { templateId, category });

        if (!templateId || !category) {
            console.error('❌ Missing template data:', { templateId, category });
            this.showError('Template data missing. Please try selecting again.');
            return;
        }

        console.log('✅ Template selected:', templateId, category);

        // Update visual selection
        document.querySelectorAll('.template-option').forEach(el => {
            el.classList.remove('selected');
        });
        element.classList.add('selected');
        console.log('✅ Visual selection updated');

        // Load template
        try {
            this.loadTemplate(category, templateId);
        } catch (error) {
            console.error('❌ Error loading template:', error);
            this.showError(`Failed to load template: ${error.message}`);
        }
    }

    /**
     * Load template onto canvas
     */
    loadTemplate(category, templateId) {
        console.log('🔄 Loading template:', category, templateId);

        const template = this.modules.templates.getTemplate(category, templateId);
        if (!template) {
            console.error('❌ Template not found:', category, templateId);
            this.showError(`Template not found: ${templateId}`);
            return;
        }

        console.log('📋 Template data:', template);

        // Check if canvas is available
        if (!this.modules.canvas) {
            console.error('❌ Canvas not initialized');
            this.showError('Canvas not ready. Please refresh the page.');
            return;
        }

        try {
            // Clear canvas
            console.log('🗑️ Clearing canvas...');
            this.modules.canvas.clear();

            // Set template in template manager
            this.modules.templates.setCurrentCategory(category);
            this.modules.templates.setSelectedTemplate(templateId);

            // Set category in background manager
            this.modules.background.setCurrentCategory(category);

            // Load background
            console.log('🖼️ Loading background...');
            this.loadTemplateBackground(template);

            // Load text elements
            console.log('📝 Loading text elements...');
            this.loadTemplateTexts(template);

            // Load default image if applicable
            if (template.imagePosition && template.defaultImage) {
                console.log('🖼️ Loading default image...');
                this.modules.imageReplacement.loadDefaultImage(template);
            }

            console.log('✅ Template loaded successfully');
            this.emit('template:loaded', { category, templateId, template });

        } catch (error) {
            console.error('❌ Error in loadTemplate:', error);
            this.showError(`Failed to load template: ${error.message}`);
        }
    }

    /**
     * Load template background
     */
    loadTemplateBackground(template) {
        const canvas = this.modules.canvas.getCanvas();

        if (template.background) {
            console.log('🖼️ Loading background image:', template.background);
            this.modules.canvas.setBackgroundImage(template.background, (img) => {
                if (img) {
                    console.log('✅ Background image loaded and scaled properly');
                    console.log('📐 Image dimensions:', `${img.width}x${img.height}`);
                    console.log('📐 Scale applied:', `${img.scaleX}x${img.scaleY}`);
                } else {
                    console.error('❌ Failed to load background image');
                }
            });
        } else if (template.defaultBackground) {
            console.log('🎨 Setting background color:', template.defaultBackground);
            canvas.setBackgroundColor(template.defaultBackground, canvas.renderAll.bind(canvas));
            console.log('✅ Background color set');
        } else {
            console.log('⚪ No background specified, using default');
            canvas.setBackgroundColor('#ffffff', canvas.renderAll.bind(canvas));
        }
    }

    /**
     * Load template text elements
     */
    loadTemplateTexts(template) {
        const canvas = this.modules.canvas.getCanvas();

        if (!template.textPositions || !template.textStyles || !template.defaultTexts) {
            console.warn('⚠️ Template missing text data');
            return;
        }

        console.log(`📝 Adding ${template.textPositions.length} text elements`);

        // Store text objects for reference
        this.textObjects = [];

        template.textPositions.forEach((position, index) => {
            try {
                const textStyle = template.textStyles[index];
                const defaultText = template.defaultTexts[index];

                if (!textStyle || !defaultText) {
                    console.warn(`⚠️ Missing text data for index ${index}`);
                    return;
                }

                const text = new fabric.Text(defaultText, {
                    left: position.x,
                    top: position.y,
                    fill: textStyle.color || '#000000',
                    fontSize: parseInt(textStyle.fontSize) || 24,
                    fontFamily: textStyle.fontFamily || 'Arial',
                    fontWeight: textStyle.fontWeight || 'normal',
                    fontStyle: textStyle.fontStyle || 'normal',
                    shadow: textStyle.textShadow || null,
                    originX: position.align === 'center' ? 'center' : 'left',
                    originY: 'top',
                    textIndex: index // Add index for reference
                });

                canvas.add(text);
                this.textObjects[index] = text;
                console.log(`✅ Added text element ${index + 1}: "${defaultText}"`);

            } catch (error) {
                console.error(`❌ Error adding text element ${index}:`, error);
            }
        });

        canvas.renderAll();
        console.log('✅ All text elements added');

        // Populate text fields in the left panel
        this.populateTextFields(template);
    }

    /**
     * Populate text fields in the left panel
     */
    populateTextFields(template) {
        const container = document.getElementById('textFieldsContainer');
        const section = document.getElementById('textEditingSection');

        if (!container || !section) {
            console.warn('⚠️ Text fields container or section not found');
            return;
        }

        // Show the text editing section
        section.style.display = 'block';

        // Clear existing content
        container.innerHTML = '';

        // Create text fields for each text element
        template.defaultTexts.forEach((defaultText, index) => {
            const fieldGroup = document.createElement('div');
            fieldGroup.className = 'text-field-group';
            fieldGroup.style.cssText = `
                margin-bottom: 15px;
                padding: 15px;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background: #fafafa;
            `;

            fieldGroup.innerHTML = `
                <label style="
                    display: block;
                    font-weight: 600;
                    margin-bottom: 8px;
                    color: #333;
                    font-size: 14px;
                ">Text ${index + 1}:</label>
                <div style="
                    display: flex;
                    gap: 8px;
                    align-items: center;
                    width: 100%;
                ">
                    <input
                        type="text"
                        id="textField${index}"
                        value="${defaultText}"
                        data-text-index="${index}"
                        style="
                            flex: 1;
                            padding: 10px;
                            border: 2px solid #e0e0e0;
                            border-radius: 4px;
                            font-size: 14px;
                            transition: border-color 0.2s;
                            box-sizing: border-box;
                            min-width: 0;
                        "
                        placeholder="Enter text..."
                    >
                    <button
                        class="customize-btn-inline"
                        data-text-index="${index}"
                        style="
                            background: #007bff;
                            color: white;
                            border: none;
                            padding: 10px 12px;
                            border-radius: 4px;
                            font-size: 14px;
                            cursor: pointer;
                            transition: all 0.2s;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            flex-shrink: 0;
                            width: 40px;
                            height: 40px;
                        "
                        title="Customize Text ${index + 1}"
                    >
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            `;

            container.appendChild(fieldGroup);

            // Add real-time text update listener
            const textInput = fieldGroup.querySelector(`#textField${index}`);
            textInput.addEventListener('input', (e) => {
                this.updateTextContent(index, e.target.value);
            });

            // Add focus styling
            textInput.addEventListener('focus', (e) => {
                e.target.style.borderColor = '#007bff';
                e.target.style.boxShadow = '0 0 0 3px rgba(0,123,255,0.1)';
            });

            textInput.addEventListener('blur', (e) => {
                e.target.style.borderColor = '#e0e0e0';
                e.target.style.boxShadow = 'none';
            });

            // Add customize button event listener
            const customizeBtn = fieldGroup.querySelector('.customize-btn-inline');
            customizeBtn.addEventListener('click', () => {
                this.showTextStylePanel(index);
            });

            // Add hover effects for customize button
            customizeBtn.addEventListener('mouseenter', (e) => {
                e.target.style.backgroundColor = '#0056b3';
                e.target.style.transform = 'scale(1.05)';
            });

            customizeBtn.addEventListener('mouseleave', (e) => {
                e.target.style.backgroundColor = '#007bff';
                e.target.style.transform = 'scale(1)';
            });
        });

        console.log(`✅ Created ${template.defaultTexts.length} text input fields with inline customize buttons`);

        // Show image replacement section if template has default image
        this.updateImageReplacementSection(template);

        // Show background change section
        this.modules.background.showBackgroundSection();
    }



    /**
     * Update image replacement section visibility and info
     */
    updateImageReplacementSection(template) {
        const section = document.getElementById('imageReplacementSection');
        const infoText = document.querySelector('.image-info-text');

        if (!section) return;

        if (template.defaultImage && template.imagePosition) {
            // Show the section
            section.style.display = 'block';

            // Update info text
            if (infoText) {
                const imageName = template.defaultImage.split('/').pop();
                infoText.textContent = `Current: ${imageName}`;
            }

            console.log('✅ Image replacement section shown');
        } else {
            // Hide the section
            section.style.display = 'none';
            console.log('ℹ️ No default image - image replacement section hidden');
        }
    }

    /**
     * Update text content in real-time
     */
    updateTextContent(textIndex, newText) {
        if (!this.textObjects || !this.textObjects[textIndex]) {
            console.warn(`⚠️ Text object ${textIndex} not found`);
            return;
        }

        const textObject = this.textObjects[textIndex];
        textObject.set('text', newText);

        const canvas = this.modules.canvas.getCanvas();
        canvas.renderAll();

        console.log(`📝 Updated text ${textIndex + 1}: "${newText}"`);
    }

    /**
     * Show text style customization panel for specific text
     */
    showTextStylePanel(textIndex) {
        if (!this.textObjects || !this.textObjects[textIndex]) {
            console.warn(`⚠️ Text object ${textIndex} not found`);
            return;
        }

        const textObject = this.textObjects[textIndex];

        // Select the text object on canvas
        const canvas = this.modules.canvas.getCanvas();
        canvas.setActiveObject(textObject);
        canvas.renderAll();

        // Show the style customization panel
        this.showTextCustomizationPanel(textObject, textIndex);
    }

    /**
     * Load template image
     */
    loadTemplateImage(template) {
        const canvas = this.modules.canvas.getCanvas();

        console.log('🖼️ Loading template image:', template.defaultImage);
        fabric.Image.fromURL(template.defaultImage, (img) => {
            if (img) {
                // Get current zoom for debugging
                const currentZoom = canvas.getZoom();

                console.log('📐 Image positioning:', {
                    position: template.imagePosition,
                    imageSize: `${img.width}x${img.height}`,
                    canvasZoom: currentZoom
                });

                img.set({
                    left: template.imagePosition.x,
                    top: template.imagePosition.y,
                    scaleX: template.imagePosition.width / img.width,
                    scaleY: template.imagePosition.height / img.height
                });

                canvas.add(img);
                canvas.renderAll();
                console.log('✅ Template image loaded');
            } else {
                console.error('❌ Failed to load template image');
            }
        }, {
            crossOrigin: 'anonymous'
        });
    }

    /**
     * Handle object selection
     */
    handleObjectSelection(detail) {
        console.log('Object selected:', detail.object);
        this.selectedObject = detail.object;

        // Note: Modal/dialog behavior removed - users can use left panel controls instead
        // The selected object is stored for potential use by left panel controls
    }

    /**
     * Handle object deselection
     */
    handleObjectDeselection() {
        console.log('Object deselected');
        this.selectedObject = null;
        // Note: Modal/dialog cleanup removed - no modals to close
    }

    /**
     * Handle object modification
     */
    handleObjectModification(detail) {
        console.log('Object modified:', detail.object);
        // Auto-save or update state
    }

    /**
     * Show object properties panel (DISABLED - no modal behavior)
     */
    showObjectProperties() {
        // Modal/dialog behavior disabled - users should use left panel controls instead
        console.log('Object properties panel disabled - use left panel controls');
        return;
    }

    /**
     * Hide object properties panel
     */
    hideObjectProperties() {
        const propertiesPanel = document.getElementById('propertiesPanel');
        if (propertiesPanel) {
            propertiesPanel.style.display = 'none';
        }
    }

    /**
     * Show text customization panel
     */
    showTextCustomizationPanel(textObject, textIndex = null) {
        // Remove any existing panel
        const existingPanel = document.querySelector('.text-customization-panel');
        if (existingPanel) {
            existingPanel.remove();
        }

        const panel = document.createElement('div');
        panel.className = 'text-customization-panel';
        panel.style.display = 'block';

        // Store reference to text object and index
        panel.dataset.textIndex = textIndex !== null ? textIndex : '';

        panel.innerHTML = `
            <div class="panel-header">
                <h3><i class="fas fa-palette"></i> Customize Text ${textIndex !== null ? (textIndex + 1) : ''}</h3>
                <button class="close-btn" onclick="this.closest('.text-customization-panel').remove()">&times;</button>
            </div>
            <div class="panel-content">
                <div class="form-grid">
                    <div class="form-group full-width">
                        <label for="textContent">Text Content:</label>
                        <input type="text" id="textContent" value="${textObject.text}" maxlength="100">
                    </div>

                    <div class="form-group">
                        <label for="textColor">Font Color:</label>
                        <input type="color" id="textColor" value="${textObject.fill}">
                    </div>

                    <div class="form-group">
                        <label for="fontSize">Font Size: <span id="fontSizeValue">${textObject.fontSize}px</span></label>
                        <input type="range" id="fontSize" min="12" max="72" value="${textObject.fontSize}">
                    </div>

                    <div class="form-group">
                        <label for="fontFamily">Font Family:</label>
                        <select id="fontFamily">
                            <option value="Arial" ${textObject.fontFamily === 'Arial' ? 'selected' : ''}>Arial</option>
                            <option value="Helvetica" ${textObject.fontFamily === 'Helvetica' ? 'selected' : ''}>Helvetica</option>
                            <option value="Times New Roman" ${textObject.fontFamily === 'Times New Roman' ? 'selected' : ''}>Times New Roman</option>
                            <option value="Georgia" ${textObject.fontFamily === 'Georgia' ? 'selected' : ''}>Georgia</option>
                            <option value="Verdana" ${textObject.fontFamily === 'Verdana' ? 'selected' : ''}>Verdana</option>
                            <option value="Trebuchet MS" ${textObject.fontFamily === 'Trebuchet MS' ? 'selected' : ''}>Trebuchet MS</option>
                            <option value="Impact" ${textObject.fontFamily === 'Impact' ? 'selected' : ''}>Impact</option>
                            <option value="Comic Sans MS" ${textObject.fontFamily === 'Comic Sans MS' ? 'selected' : ''}>Comic Sans MS</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="fontWeight">Font Weight:</label>
                        <select id="fontWeight">
                            <option value="100" ${textObject.fontWeight === '100' ? 'selected' : ''}>Thin</option>
                            <option value="300" ${textObject.fontWeight === '300' ? 'selected' : ''}>Light</option>
                            <option value="400" ${textObject.fontWeight === '400' || textObject.fontWeight === 'normal' ? 'selected' : ''}>Normal</option>
                            <option value="500" ${textObject.fontWeight === '500' ? 'selected' : ''}>Medium</option>
                            <option value="600" ${textObject.fontWeight === '600' ? 'selected' : ''}>Semi Bold</option>
                            <option value="700" ${textObject.fontWeight === '700' || textObject.fontWeight === 'bold' ? 'selected' : ''}>Bold</option>
                            <option value="800" ${textObject.fontWeight === '800' ? 'selected' : ''}>Extra Bold</option>
                            <option value="900" ${textObject.fontWeight === '900' ? 'selected' : ''}>Black</option>
                        </select>
                    </div>

                    <div class="form-group shadow-group full-width">
                        <div class="property-header">
                            <label class="property-label">Text Shadow</label>
                            <button class="toggle-btn shadow-toggle" id="shadowToggle" type="button" aria-label="Toggle shadow">
                                <span>S</span>
                            </button>
                        </div>

                        <div class="shadow-controls" id="shadowControls" style="display: none;">
                            <!-- Shadow Color -->
                            <div class="sub-property">
                                <label for="shadowColor" class="sub-label">Shadow Color</label>
                                <input type="color" id="shadowColor" class="color-picker" value="#000000">
                            </div>

                            <!-- Shadow Type Selector -->
                            <div class="sub-property">
                                <label for="shadowType" class="sub-label">Shadow Type</label>
                                <select id="shadowType" class="property-select">
                                    <option value="glow">Glow (No Offset)</option>
                                    <option value="drop">Drop Shadow</option>
                                </select>
                            </div>

                            <!-- Shadow Blur -->
                            <div class="sub-property">
                                <label for="shadowBlur" class="sub-label">Blur</label>
                                <input type="range" id="shadowBlur" class="range-slider" min="1" max="30" value="5">
                                <span class="range-value" id="shadowBlurValue">5</span>
                            </div>

                            <!-- Shadow Offset Controls (only for drop shadow) -->
                            <div id="offsetControls" style="display: none;">
                                <!-- Shadow Offset X -->
                                <div class="sub-property">
                                    <label for="shadowOffsetX" class="sub-label">Offset X</label>
                                    <input type="range" id="shadowOffsetX" class="range-slider" min="-20" max="20" value="2">
                                    <span class="range-value" id="shadowOffsetXValue">2</span>
                                </div>

                                <!-- Shadow Offset Y -->
                                <div class="sub-property">
                                    <label for="shadowOffsetY" class="sub-label">Offset Y</label>
                                    <input type="range" id="shadowOffsetY" class="range-slider" min="-20" max="20" value="2">
                                    <span class="range-value" id="shadowOffsetYValue">2</span>
                                </div>
                            </div>

                            <!-- Shadow Opacity -->
                            <div class="sub-property">
                                <label for="shadowOpacity" class="sub-label">Opacity</label>
                                <input type="range" id="shadowOpacity" class="range-slider" min="0" max="100" value="100">
                                <span class="range-value" id="shadowOpacityValue">100%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel-actions">
                    <button class="apply-btn" onclick="
                        console.log('🔥 BUTTON CLICKED - DIRECT CLOSE!');
                        try {
                            window.billboardEditor.applyTextChanges(this);
                        } catch(e) {
                            console.error('❌ Error calling applyTextChanges:', e);
                        }

                        // BACKUP FORCE CLOSE
                        console.log('🚨 BACKUP FORCE CLOSE');
                        const panel = this.closest('.text-customization-panel');
                        if (panel) {
                            panel.remove();
                            console.log('✅ Panel removed by backup method');
                        }

                        // NUCLEAR OPTION - Remove ALL panels
                        document.querySelectorAll('.text-customization-panel').forEach(p => p.remove());
                        console.log('💥 NUCLEAR CLOSE - All panels removed');
                    ">Apply Changes</button>
                </div>
            </div>
        `;

        document.body.appendChild(panel);

        // Add real-time preview listeners
        this.addTextPreviewListeners(textObject, textIndex);

        console.log(`🎨 Text customization panel opened for text ${textIndex !== null ? (textIndex + 1) : ''}`);
    }

    /**
     * Add real-time preview listeners to text customization panel
     */
    addTextPreviewListeners(textObject, textIndex = null) {
        const textContent = document.getElementById('textContent');
        const textColor = document.getElementById('textColor');
        const fontSize = document.getElementById('fontSize');
        const fontSizeValue = document.getElementById('fontSizeValue');
        const fontFamily = document.getElementById('fontFamily');
        const fontWeight = document.getElementById('fontWeight');

        if (textContent) {
            textContent.addEventListener('input', (e) => {
                textObject.set('text', e.target.value);
                this.modules.canvas.getCanvas().renderAll();

                // Update corresponding text field in left panel if textIndex is available
                if (textIndex !== null) {
                    const leftPanelField = document.getElementById(`textField${textIndex}`);
                    if (leftPanelField) {
                        leftPanelField.value = e.target.value;
                    }
                }
            });
        }

        if (textColor) {
            textColor.addEventListener('input', (e) => {
                textObject.set('fill', e.target.value);
                this.modules.canvas.getCanvas().renderAll();
            });
        }

        if (fontSize && fontSizeValue) {
            fontSize.addEventListener('input', (e) => {
                const size = parseInt(e.target.value);
                textObject.set('fontSize', size);
                fontSizeValue.textContent = size + 'px';
                this.modules.canvas.getCanvas().renderAll();
            });
        }

        if (fontFamily) {
            fontFamily.addEventListener('change', (e) => {
                textObject.set('fontFamily', e.target.value);
                this.modules.canvas.getCanvas().renderAll();
            });
        }

        if (fontWeight) {
            fontWeight.addEventListener('change', (e) => {
                textObject.set('fontWeight', e.target.value);
                this.modules.canvas.getCanvas().renderAll();
            });
        }

        // Shadow controls
        this.setupShadowControls(textObject);
    }

    /**
     * Setup shadow controls with advanced functionality
     */
    setupShadowControls(textObject) {
        const shadowToggle = document.getElementById('shadowToggle');
        const shadowControls = document.getElementById('shadowControls');
        const shadowColor = document.getElementById('shadowColor');
        const shadowType = document.getElementById('shadowType');
        const shadowBlur = document.getElementById('shadowBlur');
        const shadowBlurValue = document.getElementById('shadowBlurValue');
        const shadowOffsetX = document.getElementById('shadowOffsetX');
        const shadowOffsetY = document.getElementById('shadowOffsetY');
        const shadowOffsetXValue = document.getElementById('shadowOffsetXValue');
        const shadowOffsetYValue = document.getElementById('shadowOffsetYValue');
        const shadowOpacity = document.getElementById('shadowOpacity');
        const shadowOpacityValue = document.getElementById('shadowOpacityValue');
        const offsetControls = document.getElementById('offsetControls');

        // Initialize shadow state
        let shadowEnabled = !!textObject.shadow;
        if (shadowEnabled) {
            shadowToggle.classList.add('active');
            shadowControls.style.display = 'grid';
        }

        // Shadow toggle
        if (shadowToggle) {
            shadowToggle.addEventListener('click', () => {
                shadowEnabled = !shadowEnabled;
                shadowToggle.classList.toggle('active', shadowEnabled);
                shadowControls.style.display = shadowEnabled ? 'grid' : 'none';

                if (!shadowEnabled) {
                    textObject.set('shadow', null);
                } else {
                    this.updateShadow(textObject);
                }
                this.modules.canvas.getCanvas().renderAll();
            });
        }

        // Shadow type change
        if (shadowType) {
            shadowType.addEventListener('change', () => {
                const isDropShadow = shadowType.value === 'drop';
                offsetControls.style.display = isDropShadow ? 'block' : 'none';
                if (shadowEnabled) {
                    this.updateShadow(textObject);
                    this.modules.canvas.getCanvas().renderAll();
                }
            });
        }

        // Shadow blur
        if (shadowBlur && shadowBlurValue) {
            shadowBlur.addEventListener('input', (e) => {
                shadowBlurValue.textContent = e.target.value;
                if (shadowEnabled) {
                    this.updateShadow(textObject);
                    this.modules.canvas.getCanvas().renderAll();
                }
            });
        }

        // Shadow offset X
        if (shadowOffsetX && shadowOffsetXValue) {
            shadowOffsetX.addEventListener('input', (e) => {
                shadowOffsetXValue.textContent = e.target.value;
                if (shadowEnabled) {
                    this.updateShadow(textObject);
                    this.modules.canvas.getCanvas().renderAll();
                }
            });
        }

        // Shadow offset Y
        if (shadowOffsetY && shadowOffsetYValue) {
            shadowOffsetY.addEventListener('input', (e) => {
                shadowOffsetYValue.textContent = e.target.value;
                if (shadowEnabled) {
                    this.updateShadow(textObject);
                    this.modules.canvas.getCanvas().renderAll();
                }
            });
        }

        // Shadow opacity
        if (shadowOpacity && shadowOpacityValue) {
            shadowOpacity.addEventListener('input', (e) => {
                shadowOpacityValue.textContent = e.target.value + '%';
                if (shadowEnabled) {
                    this.updateShadow(textObject);
                    this.modules.canvas.getCanvas().renderAll();
                }
            });
        }

        // Shadow color
        if (shadowColor) {
            shadowColor.addEventListener('input', () => {
                if (shadowEnabled) {
                    this.updateShadow(textObject);
                    this.modules.canvas.getCanvas().renderAll();
                }
            });
        }
    }

    /**
     * Update shadow based on current settings
     */
    updateShadow(textObject) {
        const shadowColor = document.getElementById('shadowColor');
        const shadowType = document.getElementById('shadowType');
        const shadowBlur = document.getElementById('shadowBlur');
        const shadowOffsetX = document.getElementById('shadowOffsetX');
        const shadowOffsetY = document.getElementById('shadowOffsetY');
        const shadowOpacity = document.getElementById('shadowOpacity');

        if (!shadowColor || !shadowType || !shadowBlur || !shadowOpacity) return;

        const color = shadowColor.value;
        const blur = parseInt(shadowBlur.value);
        const opacity = parseInt(shadowOpacity.value) / 100;
        const isDropShadow = shadowType.value === 'drop';

        let offsetX = 0;
        let offsetY = 0;

        if (isDropShadow && shadowOffsetX && shadowOffsetY) {
            offsetX = parseInt(shadowOffsetX.value);
            offsetY = parseInt(shadowOffsetY.value);
        }

        // Convert hex color to rgba
        const r = parseInt(color.substr(1, 2), 16);
        const g = parseInt(color.substr(3, 2), 16);
        const b = parseInt(color.substr(5, 2), 16);
        const shadowColorRgba = `rgba(${r}, ${g}, ${b}, ${opacity})`;

        textObject.set('shadow', {
            color: shadowColorRgba,
            blur: blur,
            offsetX: offsetX,
            offsetY: offsetY
        });
    }

    /**
     * Apply text changes from customization panel
     */
    applyTextChanges(button) {
        console.log('🔥 APPLY BUTTON CLICKED - FORCING DIALOG CLOSE!', button);

        try {
            const panel = button.closest('.text-customization-panel');
            console.log('📋 Found panel:', panel);

            if (!panel) {
                console.error('❌ NO PANEL FOUND!');
                return;
            }

            const textIndex = panel.dataset.textIndex;
            console.log('📝 Text index:', textIndex);

            // Get form values
            const textContent = panel.querySelector('#textContent')?.value || '';
            const textColor = panel.querySelector('#textColor')?.value || '#000000';
            const fontSize = parseInt(panel.querySelector('#fontSize')?.value) || 24;
            const fontFamily = panel.querySelector('#fontFamily')?.value || 'Arial';
            const fontWeight = panel.querySelector('#fontWeight')?.value || '400';
            const textShadow = panel.querySelector('#textShadow')?.checked || false;

            console.log('📊 Form values:', { textContent, textColor, fontSize, fontFamily, fontWeight, textShadow });

            // Apply changes to text object
            if (textIndex !== null && textIndex !== '' && this.textObjects) {
                const index = parseInt(textIndex);
                const textObject = this.textObjects[index];

                if (textObject) {
                    console.log('✏️ Updating text object:', textObject);

                    // Update text object properties
                    textObject.set({
                        text: textContent,
                        fill: textColor,
                        fontSize: fontSize,
                        fontFamily: fontFamily,
                        fontWeight: fontWeight,
                        shadow: textShadow ? '2px 2px 4px rgba(0,0,0,0.5)' : null
                    });

                    // Update corresponding text field
                    const textField = document.getElementById(`textField${index}`);
                    if (textField) {
                        textField.value = textContent;
                    }

                    // Re-render canvas
                    const canvas = this.modules.canvas.getCanvas();
                    canvas.renderAll();
                }
            }

            // FORCE CLOSE THE PANEL - MULTIPLE METHODS
            console.log('🚪 FORCING PANEL CLOSE...');

            // Method 1: Remove the panel
            panel.remove();

            // Method 2: Hide with display none
            panel.style.display = 'none';

            // Method 3: Remove all panels with this class
            document.querySelectorAll('.text-customization-panel').forEach(p => {
                p.remove();
                console.log('🗑️ Removed panel:', p);
            });

            console.log('✅ PANEL SHOULD BE CLOSED NOW!');
            this.showSuccess('Text changes applied successfully!');

        } catch (error) {
            console.error('💥 ERROR in applyTextChanges:', error);

            // EMERGENCY CLOSE - Remove all panels regardless of error
            document.querySelectorAll('.text-customization-panel').forEach(panel => {
                panel.remove();
            });

            this.showSuccess('Text changes applied (with emergency close)!');
        }
    }

    /**
     * EMERGENCY METHOD - Force close all dialogs
     */
    forceCloseAllDialogs() {
        console.log('🚨 EMERGENCY CLOSE - Removing all dialogs!');

        // Remove all text customization panels
        document.querySelectorAll('.text-customization-panel').forEach(panel => {
            panel.remove();
            console.log('🗑️ Emergency removed panel:', panel);
        });

        // Also try to close any other modal-like elements
        document.querySelectorAll('[id*="customization"], [class*="modal"], [class*="dialog"]').forEach(element => {
            if (element.style.display !== 'none') {
                element.style.display = 'none';
                console.log('👻 Hidden element:', element);
            }
        });

        console.log('✅ Emergency close complete!');
    }

    /**
     * Remove selected text object
     */
    removeSelectedText(button) {
        const panel = button.closest('.text-customization-panel');
        const textIndex = panel ? panel.dataset.textIndex : null;

        if (textIndex !== null && textIndex !== '') {
            const index = parseInt(textIndex);
            if (this.textObjects && this.textObjects[index]) {
                const canvas = this.modules.canvas.getCanvas();
                canvas.remove(this.textObjects[index]);
                this.textObjects[index] = null;
                canvas.renderAll();

                // Clear the corresponding text field
                const textField = document.getElementById(`textField${index}`);
                if (textField) {
                    textField.value = '';
                }

                this.showSuccess('Text removed successfully!');
            }
        }

        if (panel) {
            panel.remove();
        }
    }

    /**
     * Create text customization panel
     */
    createTextCustomizationPanel() {
        const panel = document.createElement('div');
        panel.id = 'textCustomizationPanel';
        panel.className = 'text-customization-panel';

        panel.innerHTML = `
            <div class="panel-header">
                <h3><i class="fas fa-edit"></i> Text Customization</h3>
                <button class="close-btn" onclick="billboardEditor.closeTextCustomization()">×</button>
            </div>
            <div class="panel-content">
                <div class="form-group">
                    <label for="textContent">Text Content:</label>
                    <input type="text" id="textContent" placeholder="Enter text...">
                </div>

                <div class="form-group">
                    <label for="textColor">Font Color:</label>
                    <input type="color" id="textColor" value="#000000">
                </div>

                <div class="form-group">
                    <label for="fontSize">Font Size: <span id="fontSizeValue">24px</span></label>
                    <input type="range" id="fontSize" min="12" max="72" value="24">
                </div>

                <div class="form-group">
                    <label for="fontFamily">Font Family:</label>
                    <select id="fontFamily">
                        <option value="Arial">Arial</option>
                        <option value="Helvetica">Helvetica</option>
                        <option value="Times New Roman">Times New Roman</option>
                        <option value="Georgia">Georgia</option>
                        <option value="Verdana">Verdana</option>
                        <option value="Courier New">Courier New</option>
                        <option value="Impact">Impact</option>
                        <option value="Comic Sans MS">Comic Sans MS</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="fontWeight">Font Weight:</label>
                    <select id="fontWeight">
                        <option value="100">Thin</option>
                        <option value="300">Light</option>
                        <option value="400">Normal</option>
                        <option value="500">Medium</option>
                        <option value="600">Semi Bold</option>
                        <option value="700">Bold</option>
                        <option value="800">Extra Bold</option>
                        <option value="900">Black</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="textShadow"> Add Text Shadow
                    </label>
                </div>

                <div class="form-group">
                    <button id="applyTextChanges" class="apply-btn">Apply Changes</button>
                </div>
            </div>
        `;

        document.body.appendChild(panel);
        this.bindTextCustomizationEvents();
        return panel;
    }

    /**
     * Bind text customization events
     */
    bindTextCustomizationEvents() {
        // Text content change
        document.getElementById('textContent').addEventListener('input', (e) => {
            this.updateTextProperty('text', e.target.value);
        });

        // Color change
        document.getElementById('textColor').addEventListener('change', (e) => {
            this.updateTextProperty('fill', e.target.value);
        });

        // Font size change
        document.getElementById('fontSize').addEventListener('input', (e) => {
            const size = parseInt(e.target.value);
            document.getElementById('fontSizeValue').textContent = size + 'px';
            this.updateTextProperty('fontSize', size);
        });

        // Font family change
        document.getElementById('fontFamily').addEventListener('change', (e) => {
            this.updateTextProperty('fontFamily', e.target.value);
        });

        // Font weight change
        document.getElementById('fontWeight').addEventListener('change', (e) => {
            this.updateTextProperty('fontWeight', e.target.value);
        });

        // Text shadow toggle
        document.getElementById('textShadow').addEventListener('change', (e) => {
            const shadow = e.target.checked ? '2px 2px 4px rgba(0,0,0,0.5)' : '';
            this.updateTextProperty('shadow', shadow);
        });

        // Apply button
        document.getElementById('applyTextChanges').addEventListener('click', () => {
            this.applyTextChanges();
        });
    }

    /**
     * Populate text customization panel with current values
     */
    populateTextCustomizationPanel(textObject) {
        document.getElementById('textContent').value = textObject.text || '';
        document.getElementById('textColor').value = textObject.fill || '#000000';
        document.getElementById('fontSize').value = textObject.fontSize || 24;
        document.getElementById('fontSizeValue').textContent = (textObject.fontSize || 24) + 'px';
        document.getElementById('fontFamily').value = textObject.fontFamily || 'Arial';
        document.getElementById('fontWeight').value = textObject.fontWeight || '400';
        document.getElementById('textShadow').checked = !!textObject.shadow;
    }

    /**
     * Update text property in real-time
     */
    updateTextProperty(property, value) {
        if (!this.selectedObject || this.selectedObject.type !== 'text') return;

        this.selectedObject.set(property, value);
        this.modules.canvas.getCanvas().renderAll();
    }

    /**
     * Apply all text changes
     */
    applyTextChanges() {
        if (!this.selectedObject || this.selectedObject.type !== 'text') return;

        const canvas = this.modules.canvas.getCanvas();
        canvas.renderAll();
        this.showSuccess('Text changes applied successfully!');

        // Close the customization dialog
        this.closeTextCustomization();
    }

    /**
     * Close text customization panel
     */
    closeTextCustomization() {
        const panel = document.getElementById('textCustomizationPanel');
        if (panel) {
            panel.style.display = 'none';
        }
    }

    /**
     * Show text properties (DISABLED - no modal behavior)
     */
    showTextProperties() {
        // Text customization modal disabled - users should use left panel text fields instead
        console.log('Text properties modal disabled - use left panel text fields');
        return;
    }

    /**
     * Show image upload panel (DISABLED - no modal behavior)
     */
    showImageUploadPanel() {
        // Image upload modal disabled - users should use left panel image replacement instead
        console.log('Image upload modal disabled - use left panel image replacement');
        return;
    }

    /**
     * Create image upload panel
     */
    createImageUploadPanel() {
        const panel = document.createElement('div');
        panel.id = 'imageUploadPanel';
        panel.className = 'image-upload-panel';

        panel.innerHTML = `
            <div class="panel-header">
                <h3>🖼️ Image Upload</h3>
                <button class="close-btn" onclick="billboardEditor.closeImageUpload()">×</button>
            </div>
            <div class="panel-content">
                <div class="form-group">
                    <label for="imageUpload">Upload New Image:</label>
                    <input type="file" id="imageUpload" accept="image/*">
                    <div class="upload-info">
                        Supported formats: JPG, PNG, GIF, WebP<br>
                        Max size: 5MB
                    </div>
                </div>

                <div class="form-group">
                    <div class="image-preview" id="imagePreview">
                        <div class="preview-placeholder">
                            📷 Image preview will appear here
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <button id="applyImageUpload" class="apply-btn" disabled>Apply Image</button>
                    <button id="removeImage" class="remove-btn">Remove Image</button>
                </div>
            </div>
        `;

        document.body.appendChild(panel);
        this.bindImageUploadEvents();
        return panel;
    }

    /**
     * Bind image upload events
     */
    bindImageUploadEvents() {
        const fileInput = document.getElementById('imageUpload');
        const previewDiv = document.getElementById('imagePreview');
        const applyBtn = document.getElementById('applyImageUpload');
        const removeBtn = document.getElementById('removeImage');

        // File input change
        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                this.handleImageFileUpload(file, previewDiv, applyBtn);
            }
        });

        // Apply button
        applyBtn.addEventListener('click', () => {
            this.applyImageUpload();
        });

        // Remove button
        removeBtn.addEventListener('click', () => {
            this.removeSelectedImage();
        });
    }

    /**
     * Handle image file upload
     */
    handleImageFileUpload(file, previewDiv, applyBtn) {
        // Validate file
        if (!file.type.startsWith('image/')) {
            this.showError('Please select a valid image file.');
            return;
        }

        if (file.size > 5 * 1024 * 1024) { // 5MB limit
            this.showError('Image file is too large. Please select a file under 5MB.');
            return;
        }

        console.log('📁 Processing image file:', file.name, file.size);

        // Create file reader
        const reader = new FileReader();
        reader.onload = (e) => {
            const imageDataUrl = e.target.result;

            // Show preview
            previewDiv.innerHTML = `
                <img src="${imageDataUrl}" alt="Preview" style="max-width: 100%; max-height: 200px; border-radius: 4px;">
                <div class="preview-info">
                    <strong>${file.name}</strong><br>
                    Size: ${(file.size / 1024).toFixed(1)} KB
                </div>
            `;

            // Enable apply button
            applyBtn.disabled = false;

            // Store image data for later use
            this.pendingImageUpload = imageDataUrl;

            console.log('✅ Image preview loaded');
        };

        reader.onerror = () => {
            this.showError('Failed to read image file.');
        };

        reader.readAsDataURL(file);
    }

    /**
     * Apply image upload to selected object or canvas
     */
    applyImageUpload() {
        if (!this.pendingImageUpload) {
            this.showError('No image selected.');
            return;
        }

        console.log('🖼️ Applying image upload...');

        fabric.Image.fromURL(this.pendingImageUpload, (img) => {
            if (!img) {
                this.showError('Failed to load image.');
                return;
            }

            const canvas = this.modules.canvas.getCanvas();

            if (this.selectedObject && this.selectedObject.type === 'image') {
                // Replace existing image
                const oldImg = this.selectedObject;

                // Copy position and scale from old image
                img.set({
                    left: oldImg.left,
                    top: oldImg.top,
                    scaleX: oldImg.scaleX,
                    scaleY: oldImg.scaleY,
                    angle: oldImg.angle
                });

                // Remove old image and add new one
                canvas.remove(oldImg);
                canvas.add(img);
                canvas.setActiveObject(img);

                console.log('✅ Replaced existing image');
            } else {
                // Add new image to center of canvas
                const canvasCenter = canvas.getCenter();

                // Scale image to fit reasonably on canvas
                const maxWidth = canvas.width * 0.4;
                const maxHeight = canvas.height * 0.4;
                const scale = Math.min(maxWidth / img.width, maxHeight / img.height);

                img.set({
                    left: canvasCenter.left,
                    top: canvasCenter.top,
                    scaleX: scale,
                    scaleY: scale,
                    originX: 'center',
                    originY: 'center'
                });

                canvas.add(img);
                canvas.setActiveObject(img);

                console.log('✅ Added new image to canvas');
            }

            canvas.renderAll();
            this.showSuccess('Image uploaded successfully!');
            this.closeImageUpload();

        }, {
            crossOrigin: 'anonymous'
        });
    }

    /**
     * Remove selected image
     */
    removeSelectedImage() {
        if (!this.selectedObject || this.selectedObject.type !== 'image') {
            this.showError('No image selected to remove.');
            return;
        }

        const canvas = this.modules.canvas.getCanvas();
        canvas.remove(this.selectedObject);
        canvas.renderAll();

        this.selectedObject = null;
        this.showSuccess('Image removed successfully!');
        this.closeImageUpload();
    }

    /**
     * Close image upload panel
     */
    closeImageUpload() {
        const panel = document.getElementById('imageUploadPanel');
        if (panel) {
            panel.style.display = 'none';
        }

        // Clear pending upload
        this.pendingImageUpload = null;
    }

    /**
     * Show image properties (DISABLED - no modal behavior)
     */
    showImageProperties() {
        // Image upload modal disabled - users should use left panel image replacement instead
        console.log('Image properties modal disabled - use left panel image replacement');
        return;
    }

    /**
     * Handle export
     */
    async handleExport() {
        if (!this.modules.canvas) return;

        const exportBtn = document.getElementById('exportBtn');
        if (exportBtn) {
            exportBtn.disabled = true;
            exportBtn.textContent = 'Exporting...';
        }

        try {
            const canvas = this.modules.canvas.getCanvas();
            const result = await ExportUtils.exportCanvas(canvas, {
                format: 'png',
                quality: 'high',
                scale: 2, // HD quality
                filename: 'billboard-design'
            });

            console.log('Export successful:', result);

            // Show success message
            this.showSuccess('Image exported successfully!');

            // Try to share on mobile devices
            if (TouchUtils.isTouchDevice() && navigator.share) {
                try {
                    await ExportUtils.shareImage(result.dataURL, 'billboard-design.png');
                } catch (shareError) {
                    console.log('Share not available, download completed');
                }
            }

        } catch (error) {
            console.error('Export failed:', error);
            this.showError('Failed to export image. Please try again.');
        } finally {
            if (exportBtn) {
                exportBtn.disabled = false;
                exportBtn.textContent = 'Export';
            }
        }
    }



    /**
     * Handle upload image button click
     */
    handleUploadImage() {
        const canvas = this.modules.canvas.getCanvas();
        const activeObject = canvas.getActiveObject();

        if (activeObject && activeObject.type === 'image') {
            // Replace selected image
            this.showImageUploadPanel(activeObject);
        } else {
            // Add new image or show existing images for replacement
            this.showImageUploadPanel(null);
        }
    }

    /**
     * Show text selection dialog for multiple text objects
     */
    showTextSelectionDialog(textObjects) {
        const dialog = document.createElement('div');
        dialog.className = 'text-selection-dialog';
        dialog.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 1001;
            padding: 20px;
            max-width: 300px;
            width: 90vw;
        `;

        dialog.innerHTML = `
            <h3 style="margin: 0 0 15px 0;"><i class="fas fa-edit"></i> Select Text to Edit</h3>
            <div class="text-list">
                ${textObjects.map((obj, index) => `
                    <button class="text-option" data-index="${index}" style="
                        display: block;
                        width: 100%;
                        padding: 10px;
                        margin: 5px 0;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        background: white;
                        cursor: pointer;
                        text-align: left;
                    ">
                        "${obj.text.substring(0, 30)}${obj.text.length > 30 ? '...' : ''}"
                    </button>
                `).join('')}
            </div>
            <button onclick="this.parentElement.remove()" style="
                width: 100%;
                padding: 10px;
                margin-top: 10px;
                border: none;
                background: #6c757d;
                color: white;
                border-radius: 4px;
                cursor: pointer;
            ">Cancel</button>
        `;

        // Add click handlers for text options
        dialog.querySelectorAll('.text-option').forEach(btn => {
            btn.addEventListener('click', () => {
                const index = parseInt(btn.dataset.index);
                const textObj = textObjects[index];
                canvas.setActiveObject(textObj);
                this.showTextCustomizationPanel(textObj);
                dialog.remove();
            });
        });

        document.body.appendChild(dialog);
    }

    /**
     * Handle clear canvas
     */
    handleClearCanvas() {
        if (confirm('Are you sure you want to clear the canvas?')) {
            this.modules.canvas.clear();
            this.modules.imageReplacement.clear();
            this.modules.background.clear();
            this.hideTextEditingSection();
            this.hideImageReplacementSection();
        }
    }

    /**
     * Handle image replacement
     */
    handleImageReplacement(file) {
        console.log('🖼️ Handling image replacement:', file.name);

        if (!this.modules.imageReplacement.hasDefaultImage()) {
            this.showError('No default image to replace. Please load a template first.');
            return;
        }

        const success = this.modules.imageReplacement.replaceDefaultImage(file);
        if (success) {
            // Update the info text
            const infoText = document.querySelector('.image-info-text');
            if (infoText) {
                infoText.textContent = `Current: ${file.name}`;
            }

            // Clear the file input
            const input = document.getElementById('imageReplacementInput');
            if (input) {
                input.value = '';
            }
        }
    }

    /**
     * Hide text editing section
     */
    hideTextEditingSection() {
        const section = document.getElementById('textEditingSection');
        if (section) {
            section.style.display = 'none';
        }
        this.textObjects = [];
    }

    /**
     * Hide image replacement section
     */
    hideImageReplacementSection() {
        const section = document.getElementById('imageReplacementSection');
        if (section) {
            section.style.display = 'none';
        }
    }

    /**
     * Clear template grid
     */
    clearTemplateGrid() {
        const templateGrid = document.getElementById('templateGrid');
        if (templateGrid) {
            templateGrid.innerHTML = '';
        }
    }

    /**
     * Update template grid layout
     */
    updateTemplateGrid() {
        const templateGrid = document.getElementById('templateGrid');
        if (!templateGrid) return;
        
        const state = this.modules.responsive.getState();
        
        // Adjust grid columns based on breakpoint
        if (state.isMobile) {
            templateGrid.style.gridTemplateColumns = 'repeat(2, 1fr)';
        } else if (state.isTablet) {
            templateGrid.style.gridTemplateColumns = 'repeat(3, 1fr)';
        } else {
            templateGrid.style.gridTemplateColumns = 'repeat(4, 1fr)';
        }
    }

    /**
     * Update canvas UI
     */
    updateCanvasUI() {
        // Any canvas-specific UI updates
    }

    /**
     * Show error message
     */
    showError(message) {
        // Create mobile-friendly toast notification
        this.showToast(message, 'error');
        console.error(message);
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        this.showToast(message, 'success');
        console.log(message);
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;

        // Style the toast
        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            left: '50%',
            transform: 'translateX(-50%)',
            background: type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#007bff',
            color: 'white',
            padding: '12px 24px',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '10000',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            opacity: '0',
            transition: 'opacity 0.3s ease',
            maxWidth: '90vw',
            textAlign: 'center'
        });

        document.body.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.style.opacity = '1';
        }, 10);

        // Remove after delay
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                if (toast.parentNode) {
                    document.body.removeChild(toast);
                }
            }, 300);
        }, 3000);

        // Add haptic feedback on mobile
        if (TouchUtils.isTouchDevice()) {
            TouchUtils.hapticFeedback(type === 'error' ? 'error' : 'success');
        }
    }

    /**
     * Emit custom events
     */
    emit(eventName, data) {
        const event = new CustomEvent(eventName, { detail: data });
        document.dispatchEvent(event);
    }

    /**
     * Get module instance
     */
    getModule(name) {
        return this.modules[name];
    }

    /**
     * Check if editor is initialized
     */
    isReady() {
        return this.isInitialized;
    }

    /**
     * Destroy editor and clean up
     */
    destroy() {
        Object.values(this.modules).forEach(module => {
            if (module && typeof module.destroy === 'function') {
                module.destroy();
            }
        });
        
        this.modules = {};
        this.isInitialized = false;
    }
}

// Initialize the application
window.billboardEditor = new BillboardEditor();

// Export for external access
window.BillboardEditor = BillboardEditor;
